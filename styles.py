ASUKA_WARM_CSS = """
:root {
    /* 🎨 原有色彩（蓝紫基调） */
    --bg-grad-start: #B6D3E2; 
    --bg-grad-mid1: #A8C8DA; 
    --bg-grad-mid2: #F1D1DB; 
    --bg-grad-end: #DEA3C1; 
    --bg-white-blend: #E1BBC9; 
    
    /* 🌸 新增暖色调（淡桃红、珊瑚红、清新蓝）- 融入白色优雅版 */
    --warm-peach: #FFF0E6;        /* 淡桃红（增加白色） */
    --coral-pink: #FFCCC7;        /* 珊瑚红（增加白色） */
    --fresh-blue: #E6F7FF;        /* 清新蓝色（增加白色） */
    --soft-lavender: #F6F0FF;     /* 淡薰衣草色（增加白色） */
    --warm-mint: #F0FFF4;         /* 暖薄荷绿（增加白色） */
    --pure-white: #FFFFFF;        /* 纯白色 */
    --soft-white: #FEFEFE;        /* 柔和白色 */
    
    /* 📦 界面元素颜色 */
    --content-bg: #FFF8F0; 
    --title-color: #F7C9C1; 
    --text-color: #F7C9C1; 
    --interactive-color1: #F2A79E; 
    --interactive-color2: #F7C9C1; 
    --hover-shadow: rgba(222, 163, 193, 0.3); 
}
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
html, body {
    background: linear-gradient(135deg,
        var(--fresh-blue) 0%,         /* 清新蓝色开始（白色调） */
        var(--pure-white) 8%,         /* 纯白色过渡 */
        var(--bg-grad-start) 15%,     /* 原清新蓝色 */
        var(--warm-peach) 25%,        /* 淡桃红色（白色调） */
        var(--soft-white) 35%,        /* 柔和白色中心 */
        var(--coral-pink) 45%,        /* 珊瑚红色（白色调） */
        var(--bg-grad-mid2) 55%,      /* 原粉紫色 */
        var(--soft-lavender) 65%,     /* 淡薰衣草色（白色调） */
        var(--pure-white) 75%,        /* 纯白色过渡 */
        var(--warm-mint) 85%,         /* 暖薄荷绿（白色调） */
        var(--bg-grad-mid1) 95%,      /* 原蓝色 */
        var(--soft-white) 100%        /* 柔和白色结尾 */
    ) !important;
    background-size: 400% 400% !important;
    animation: asukaWarmFlow 25s ease-in-out infinite !important;
    min-height: 100vh !important;
    font-family: 'Noto Sans SC', -apple-system, sans-serif !important;
    position: relative !important;
    overflow-x: hidden !important;
    filter: brightness(1.02) saturate(1.05) !important;
}
.gradio-container,
body > div:first-child {
    background: linear-gradient(135deg,
        var(--bg-grad-start) 0%,      /* 清新蓝色开始 */
        var(--fresh-blue) 12%,        /* 新增：清新蓝色过渡 */
        var(--bg-grad-mid2) 20%,      /* 原有粉紫色 */
        var(--warm-peach) 35%,        /* 新增：淡桃红色（中间区域） */
        var(--coral-pink) 45%,        /* 新增：珊瑚红色（中间区域） */
        var(--bg-grad-end) 55%,       /* 原有紫色 */
        var(--soft-lavender) 70%,     /* 新增：淡薰衣草色过渡 */
        var(--bg-grad-mid1) 80%,      /* 原有蓝色 */
        var(--warm-mint) 90%,         /* 新增：暖薄荷绿结尾 */
        var(--bg-grad-mid2) 100%      /* 原有粉紫色结尾 */
    ) !important;
    background-size: 400% 400% !important;
    animation: asukaWarmFlow 25s ease-in-out infinite !important;
    min-height: 100vh !important;
}
@keyframes asukaWarmFlow {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 25%; }
    50% { background-position: 50% 100%; }
    75% { background-position: 25% 0%; }
    100% { background-position: 0% 50%; }
}
.community-warning-card {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.95),
        rgba(var(--content-bg), 0.9)
    ) !important;
    border: 2px solid rgba(var(--interactive-color1), 0.4) !important;
    border-radius: 20px !important;
    padding: 20px !important;
    margin: 15px 0 !important;
    box-shadow: 0 8px 25px rgba(var(--interactive-color1), 0.2) !important;
    backdrop-filter: blur(10px) !important;
}
.community-warning-card h4 {
    color: var(--title-color) !important;
    margin: 5px 0 !important;
    font-weight: 600 !important;
}
.community-warning-card p {
    color: var(--text-color) !important;
    margin: 5px 0 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}
.community-warning-card strong {
    color: var(--title-color) !important;
    font-weight: 600 !important;
}
.community-warning-card em {
    color: var(--interactive-color1) !important;
    font-style: italic !important;
}
.gradio-tabs {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.95),
        rgba(var(--content-bg), 0.9)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.3) !important;
    border-radius: 20px !important;
    padding: 20px !important;
    margin: 15px 0 !important;
    box-shadow: 0 8px 25px rgba(var(--bg-grad-start), 0.2) !important;
}
.gradio-tabs .gradio-tab-nav {
    background: transparent !important;
    border-bottom: 2px solid rgba(var(--bg-grad-start), 0.2) !important;
    margin-bottom: 20px !important;
}
.gradio-tabs .gradio-tab-nav button {
    background: linear-gradient(135deg,
        rgba(var(--bg-grad-start), 0.8),
        rgba(var(--bg-white-blend), 0.7)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.3) !important;
    border-radius: 15px 15px 0 0 !important;
    color: var(--text-color) !important; 
    font-weight: 500 !important;
    padding: 12px 20px !important;
    transition: all 0.3s ease !important;
    margin: 0 2px !important;
}
.gradio-tabs .gradio-tab-nav button:hover {
    background: linear-gradient(135deg,
        rgba(var(--interactive-color1), 0.9),
        rgba(var(--interactive-color2), 0.8)
    ) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(var(--interactive-color1), 0.4) !important;
}
.gradio-tabs .gradio-tab-nav button.selected {
    background: linear-gradient(135deg, var(--interactive-color1), var(--interactive-color2)) !important;
    color: white !important;
    font-weight: bold !important;
    border-bottom: 3px solid var(--interactive-color1) !important;
    box-shadow: 0 6px 20px rgba(var(--interactive-color1), 0.4) !important;
}
.gradio-chatbot {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.95),
        rgba(var(--content-bg), 0.9)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.3) !important;
    border-radius: 20px !important;
    padding: 20px !important;
    margin: 15px 0 !important;
    box-shadow: 0 8px 25px rgba(var(--bg-grad-start), 0.2) !important;
    min-height: 400px !important;
}
.gradio-chatbot .message {
    margin: 15px 0 !important;
}
.message.user {
    background: linear-gradient(135deg, var(--interactive-color1) 0%, var(--interactive-color2) 100%) !important;
    color: white !important; 
    border-radius: 18px 18px 5px 18px !important;
    padding: 14px 18px !important;
    margin: 12px 8px !important;
    border: 2px solid rgba(var(--interactive-color1), 0.4) !important;
    box-shadow: 0 6px 18px rgba(var(--interactive-color1), 0.4) !important;
    position: relative;
}
.message.assistant {
    background: linear-gradient(135deg, var(--content-bg) 0%, rgba(var(--bg-grad-start), 0.1) 100%) !important;
    color: var(--text-color) !important; 
    border-radius: 18px 18px 18px 5px !important;
    padding: 14px 18px !important;
    margin: 12px 8px !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.3) !important;
    box-shadow: 0 6px 18px rgba(var(--bg-grad-start), 0.2) !important;
    position: relative;
}
.gradio-textbox {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.95),
        rgba(var(--content-bg), 0.9)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.3) !important;
    border-radius: 20px !important;
    padding: 15px !important;
    margin: 15px 0 !important;
    box-shadow: 0 8px 25px rgba(var(--bg-grad-start), 0.2) !important;
}
.gradio-textbox input,
.gradio-textbox textarea,
input[type="text"],
textarea {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    color: var(--text-color) !important; 
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: none !important;
}
.gradio-textbox input:focus,
.gradio-textbox textarea:focus,
input[type="text"]:focus,
textarea:focus {
    border-color: transparent !important;
    background: transparent !important;
    box-shadow: none !important;
    transform: none !important;
    animation: none !important;
}
.gradio-button,
button {
    background: linear-gradient(135deg, var(--interactive-color1) 0%, var(--interactive-color2) 100%) !important;
    border: none !important;
    border-radius: 25px !important;
    padding: 12px 25px !important;
    color: white !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 6px 20px rgba(var(--interactive-color1), 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2) !important;
}
.gradio-button:hover,
button:hover {
    background: linear-gradient(135deg, var(--interactive-color2) 0%, var(--interactive-color1) 100%) !important; 
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(var(--interactive-color1), 0.6) !important;
}
.gradio-button::before,
button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}
.gradio-button:hover::before,
button:hover::before {
    left: 100%;
}
.happiness-records,
.test-result-card,
.thinking-analysis {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.95),
        rgba(var(--content-bg), 0.9)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.3) !important;
    border-radius: 20px !important;
    padding: 20px !important;
    box-shadow: 0 8px 25px rgba(var(--bg-grad-start), 0.2) !important;
    color: var(--text-color) !important; 
    margin: 15px 0 !important;
}

/* 系统思维相关样式 */
.thinking-intro-section {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.8),
        rgba(var(--content-bg), 0.6)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-start), 0.2) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin: 15px 0 !important;
    box-shadow: 0 4px 15px rgba(var(--bg-grad-start), 0.1) !important;
}

.thinking-step-section {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.9),
        rgba(var(--content-bg), 0.7)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-mid1), 0.3) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin: 15px 0 !important;
    box-shadow: 0 6px 20px rgba(var(--bg-grad-mid1), 0.15) !important;
    transition: all 0.3s ease !important;
}

.thinking-step-section:hover {
    box-shadow: 0 8px 25px rgba(var(--bg-grad-mid1), 0.25) !important;
    transform: translateY(-2px) !important;
}

.thinking-guidance {
    background: linear-gradient(135deg,
        rgba(var(--bg-grad-start), 0.1),
        rgba(var(--bg-grad-mid1), 0.1)
    ) !important;
    border: 1px solid rgba(var(--bg-grad-start), 0.2) !important;
    border-radius: 12px !important;
    padding: 15px !important;
    margin: 10px 0 !important;
}

.thinking-input-group {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.6),
        rgba(var(--content-bg), 0.4)
    ) !important;
    border: 1px solid rgba(var(--bg-grad-mid2), 0.3) !important;
    border-radius: 10px !important;
    padding: 15px !important;
    margin: 10px 0 !important;
}

.thinking-step-area {
    background: linear-gradient(135deg,
        rgba(var(--bg-grad-mid1), 0.1),
        rgba(var(--bg-grad-mid2), 0.1)
    ) !important;
    border: 1px solid rgba(var(--bg-grad-mid1), 0.3) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin: 15px 0 !important;
    box-shadow: 0 4px 15px rgba(var(--bg-grad-mid1), 0.1) !important;
}

.thinking-choice-btn {
    background: linear-gradient(135deg,
        var(--bg-grad-start),
        var(--bg-grad-mid1)
    ) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 25px !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 12px 24px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(var(--bg-grad-start), 0.3) !important;
}

.thinking-choice-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 6px 20px rgba(var(--bg-grad-start), 0.4) !important;
    background: linear-gradient(135deg,
        var(--bg-grad-mid1),
        var(--bg-grad-mid2)
    ) !important;
}

.thinking-btn {
    background: linear-gradient(135deg,
        var(--bg-grad-mid1),
        var(--bg-grad-mid2)
    ) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 20px !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 10px 20px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 3px 12px rgba(var(--bg-grad-mid1), 0.3) !important;
}

.thinking-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 18px rgba(var(--bg-grad-mid1), 0.4) !important;
    background: linear-gradient(135deg,
        var(--bg-grad-mid2),
        var(--bg-grad-end)
    ) !important;
}

.thinking-input {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.8),
        rgba(var(--content-bg), 0.6)
    ) !important;
    border: 2px solid rgba(var(--bg-grad-mid2), 0.3) !important;
    border-radius: 12px !important;
    padding: 12px !important;
    color: var(--text-color) !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(var(--bg-grad-mid2), 0.1) !important;
}

.thinking-input:focus {
    border-color: var(--bg-grad-start) !important;
    box-shadow: 0 4px 15px rgba(var(--bg-grad-start), 0.2) !important;
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.9),
        rgba(var(--content-bg), 0.7)
    ) !important;
}

.thinking-step-what {
    border-left: 4px solid var(--bg-grad-start) !important;
}

.thinking-step-why {
    border-left: 4px solid var(--bg-grad-mid1) !important;
}

.thinking-step-how {
    border-left: 4px solid var(--bg-grad-mid2) !important;
}

.thinking-feedback {
    background: linear-gradient(135deg,
        rgba(var(--bg-grad-start), 0.1),
        rgba(var(--bg-grad-mid1), 0.1)
    ) !important;
    border: 1px solid rgba(var(--bg-grad-start), 0.3) !important;
    border-radius: 10px !important;
    padding: 15px !important;
    margin: 10px 0 !important;
    color: var(--text-color) !important;
    font-weight: 500 !important;
}

.thinking-progress {
    background: linear-gradient(135deg,
        rgba(var(--content-bg), 0.6),
        rgba(var(--content-bg), 0.4)
    ) !important;
    border: 1px solid rgba(var(--bg-grad-mid2), 0.3) !important;
    border-radius: 15px !important;
    padding: 15px !important;
    margin: 20px 0 !important;
    box-shadow: 0 3px 10px rgba(var(--bg-grad-mid2), 0.1) !important;
}

.thinking-progress span {
    transition: all 0.3s ease !important;
}

.thinking-progress span:not(.active) {
    opacity: 0.6 !important;
}

.thinking-progress span.active {
    background: linear-gradient(135deg,
        var(--bg-grad-start),
        var(--bg-grad-mid1)
    ) !important;
    color: white !important;
    box-shadow: 0 3px 10px rgba(var(--bg-grad-start), 0.3) !important;
}
::-webkit-scrollbar {
    width: 8px;
}
::-webkit-scrollbar-track {
    background: linear-gradient(135deg, rgba(var(--bg-grad-mid2), 0.5), rgba(var(--bg-white-blend), 0.5));
    border-radius: 10px;
}
::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--bg-grad-start), var(--bg-grad-mid2));
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}
::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--bg-grad-mid1), var(--bg-grad-end));
}
.gradio-container * {
    color: var(--text-color) !important; 
}
.gradio-container h1,
.gradio-container h2,
.gradio-container h3,
.gradio-container h4 {
    color: var(--title-color) !important; 
    font-weight: 600;
}
.gradio-container label {
    color: var(--text-color) !important; 
    font-weight: 500;
}
"""
def get_modelscope_css():
    """获取魔搭专用的CSS样式 - 针对iframe环境重新设计"""
    return """
    * {
        background-image: none !important;
        background-color: transparent !important;
    }
    html, body {
        background: linear-gradient(135deg,
            var(--bg-grad-start) 0%,      /* 清新蓝色开始 */
            var(--fresh-blue) 12%,        /* 新增：清新蓝色过渡 */
            var(--bg-grad-mid2) 20%,      /* 原有粉紫色 */
            var(--warm-peach) 35%,        /* 新增：淡桃红色（中间区域） */
            var(--coral-pink) 45%,        /* 新增：珊瑚红色（中间区域） */
            var(--bg-grad-end) 55%,       /* 原有紫色 */
            var(--soft-lavender) 70%,     /* 新增：淡薰衣草色过渡 */
            var(--bg-grad-mid1) 80%,      /* 原有蓝色 */
            var(--warm-mint) 90%,         /* 新增：暖薄荷绿结尾 */
            var(--bg-grad-mid2) 100%      /* 原有粉紫色结尾 */
        ) !important;
        background-size: 400% 400% !important;
        min-height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    #root, #gradio-app, .gradio-container,
    div[data-testid="container"], 
    div[class*="gradio"], div[id*="gradio"],
    div[class*="app"], div[id*="app"],
    div[class*="main"], div[id*="main"],
    div[class*="root"], div[id*="root"],
    div[class*="container"], div[id*="container"],
    div[class*="gradio-container"], div[id*="gradio-container"],
    div[class*="app-container"], div[id*="app-container"],
    div[class*="main-container"], div[id*="main-container"] {
        background: linear-gradient(135deg,
            var(--bg-grad-start) 0%,      /* 清新蓝色开始 */
            var(--fresh-blue) 12%,        /* 新增：清新蓝色过渡 */
            var(--bg-grad-mid2) 20%,      /* 原有粉紫色 */
            var(--warm-peach) 35%,        /* 新增：淡桃红色（中间区域） */
            var(--coral-pink) 45%,        /* 新增：珊瑚红色（中间区域） */
            var(--bg-grad-end) 55%,       /* 原有紫色 */
            var(--soft-lavender) 70%,     /* 新增：淡薰衣草色过渡 */
            var(--bg-grad-mid1) 80%,      /* 原有蓝色 */
            var(--warm-mint) 90%,         /* 新增：暖薄荷绿结尾 */
            var(--bg-grad-mid2) 100%      /* 原有粉紫色结尾 */
        ) !important;
        background-size: 400% 400% !important;
        min-height: 100vh !important;
    }
    iframe, embed, object {
        background: linear-gradient(135deg,
            var(--bg-grad-start) 0%, var(--bg-grad-end) 25%, var(--bg-grad-mid2) 50%, var(--bg-grad-mid1) 75%, var(--bg-white-blend) 100%
        ) !important;
    }
    @keyframes modelscopeWarmFlow {
        0% { background-position: 0% 50%; }
        25% { background-position: 100% 25%; }
        50% { background-position: 50% 100%; }
        75% { background-position: 25% 0%; }
        100% { background-position: 0% 50%; }
    }
    html, body, #root, #gradio-app, .gradio-container {
        animation: modelscopeWarmFlow 25s ease-in-out infinite !important;
    }
    .gradio-container {
        background: transparent !important;
        background-image: none !important;
    }
    * {
        background-image: inherit !important;
    }
    """
def get_modelscope_html_css():
    """获取魔搭专用的HTML CSS注入 - 简化版本"""
    return f"""
    <style>
    {get_modelscope_css()}
    </style>
    """
def get_modelscope_force_css():
    """获取魔搭专用强制CSS - 简化版本"""
    return get_modelscope_css()
def get_modelscope_iframe_css():
    """获取魔搭iframe环境专用CSS - 修复版本，包装在style标签中"""
    return """
    <style>
    :root {
        /* 🎨 原有色彩（蓝紫基调） */
        --bg-grad-start: #B6D3E2; 
        --bg-grad-mid1: #A8C8DA; 
        --bg-grad-mid2: #F1D1DB; 
        --bg-grad-end: #DEA3C1; 
        --bg-white-blend: #E1BBC9; 
        
        /* 🌸 新增暖色调（淡桃红、珊瑚红、清新蓝）- 融入白色优雅版 */
        --warm-peach: #FFF0E6;        /* 淡桃红（增加白色） */
        --coral-pink: #FFCCC7;        /* 珊瑚红（增加白色） */
        --fresh-blue: #E6F7FF;        /* 清新蓝色（增加白色） */
        --soft-lavender: #F6F0FF;     /* 淡薰衣草色（增加白色） */
        --warm-mint: #F0FFF4;         /* 暖薄荷绿（增加白色） */
        --pure-white: #FFFFFF;        /* 纯白色 */
        --soft-white: #FEFEFE;        /* 柔和白色 */
        
        /* 📦 界面元素颜色 */
        --content-bg: #FFF8F0; 
        --title-color: #F7C9C1; 
        --text-color: #F7C9C1; 
        --interactive-color1: #F2A79E; 
        --interactive-color2: #F7C9C1; 
    }
    
    * {
        background-image: none !important;
        background-color: transparent !important;
    }
    
    html, body {
        background: linear-gradient(135deg,
            var(--fresh-blue) 0%,         /* 清新蓝色开始（白色调） */
            var(--pure-white) 8%,         /* 纯白色过渡 */
            var(--bg-grad-start) 15%,     /* 原清新蓝色 */
            var(--warm-peach) 25%,        /* 淡桃红色（白色调） */
            var(--soft-white) 35%,        /* 柔和白色中心 */
            var(--coral-pink) 45%,        /* 珊瑚红色（白色调） */
            var(--bg-grad-mid2) 55%,      /* 原粉紫色 */
            var(--soft-lavender) 65%,     /* 淡薰衣草色（白色调） */
            var(--pure-white) 75%,        /* 纯白色过渡 */
            var(--warm-mint) 85%,         /* 暖薄荷绿（白色调） */
            var(--bg-grad-mid1) 95%,      /* 原蓝色 */
            var(--soft-white) 100%        /* 柔和白色结尾 */
        ) !important;
        background-size: 400% 400% !important;
        min-height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
        font-family: 'Noto Sans SC', -apple-system, sans-serif !important;
    }
    
    .gradio-container {
        background: transparent !important;
    }
    
    @keyframes asukaWarmFlow {
        0% { background-position: 0% 50%; }
        25% { background-position: 100% 25%; }
        50% { background-position: 50% 100%; }
        75% { background-position: 25% 0%; }
        100% { background-position: 0% 50%; }
    }
    
    html, body {
        animation: asukaWarmFlow 25s ease-in-out infinite !important;
    }
    
    /* 按钮样式优化 - 阳光明日香风格 */
    .gradio-button,
    button {
        background: linear-gradient(135deg, var(--interactive-color1) 0%, var(--interactive-color2) 100%) !important;
        border: none !important;
        border-radius: 25px !important;
        padding: 12px 25px !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 6px 20px rgba(var(--interactive-color1), 0.4) !important;
        position: relative !important;
        overflow: hidden !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2) !important;
    }
    
    .gradio-button:hover,
    button:hover {
        background: linear-gradient(135deg, var(--interactive-color2) 0%, var(--interactive-color1) 100%) !important; 
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(var(--interactive-color1), 0.6) !important;
    }
    
    /* Tab样式优化 */
    .gradio-tabs .gradio-tab-nav button {
        background: linear-gradient(135deg,
            rgba(182, 211, 226, 0.8),
            rgba(225, 187, 201, 0.7)
        ) !important;
        border: 2px solid rgba(182, 211, 226, 0.3) !important;
        border-radius: 15px 15px 0 0 !important;
        color: var(--text-color) !important; 
        font-weight: 500 !important;
        padding: 12px 20px !important;
        transition: all 0.3s ease !important;
        margin: 0 2px !important;
    }
    
    .gradio-tabs .gradio-tab-nav button:hover {
        background: linear-gradient(135deg,
            rgba(242, 167, 158, 0.9),
            rgba(247, 201, 193, 0.8)
        ) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 15px rgba(242, 167, 158, 0.4) !important;
    }
    
    .gradio-tabs .gradio-tab-nav button.selected {
        background: linear-gradient(135deg, var(--interactive-color1), var(--interactive-color2)) !important;
        color: white !important;
        font-weight: bold !important;
        border-bottom: 3px solid var(--interactive-color1) !important;
        box-shadow: 0 6px 20px rgba(var(--interactive-color1), 0.4) !important;
    }
    
    /* 聊天框样式优化 */
    .gradio-chatbot {
        background: linear-gradient(135deg,
            rgba(255, 248, 240, 0.95),
            rgba(255, 248, 240, 0.9)
        ) !important;
        border: 2px solid rgba(182, 211, 226, 0.3) !important;
        border-radius: 20px !important;
        padding: 20px !important;
        margin: 15px 0 !important;
        box-shadow: 0 8px 25px rgba(182, 211, 226, 0.2) !important;
        min-height: 400px !important;
    }
    
    /* 输入框样式优化 */
    .gradio-textbox {
        background: linear-gradient(135deg,
            rgba(255, 248, 240, 0.95),
            rgba(255, 248, 240, 0.9)
        ) !important;
        border: 2px solid rgba(182, 211, 226, 0.3) !important;
        border-radius: 20px !important;
        padding: 15px !important;
        margin: 15px 0 !important;
        box-shadow: 0 8px 25px rgba(182, 211, 226, 0.2) !important;
    }
    
    .gradio-textbox input,
    .gradio-textbox textarea {
        background: transparent !important;
        border: none !important;
        color: var(--text-color) !important; 
        font-size: 14px !important;
    }
    </style>
    """
def get_asuka_warm_css():
    """获取明日香式温暖CSS样式（兼容性保留）"""
    return ASUKA_WARM_CSS